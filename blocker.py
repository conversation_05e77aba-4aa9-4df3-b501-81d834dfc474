# -*- coding: utf-8 -*-
import requests
import re

# --- 静态配置信息 ---
# 这些信息通常不会改变，所以放在函数外部
CONFIG = {
    "target_ip": "*************",
    "username": "wabsuper",
    "password": "whgD@955989",
    # 直接使用原始请求包中的URL编码字符串，不做任何改动
    "host_name_encoded": "01-%CD%F8%B0%B2%B7%E2%BD%FB%C4%DA%CD%F8%B5%D8%D6%B7",
}

# 因为是IP地址访问HTTPS，证书会无效，忽略requests的警告
requests.packages.urllib3.disable_warnings(requests.packages.urllib3.exceptions.InsecureRequestWarning)

def add_ip_to_blocklist(ip_to_add: str) -> bool:
    """
    将指定的IP地址添加到封禁列表。

    此函数会完整执行“登录 -> 查询现有IP -> 添加新IP并提交”的自动化流程。
    所有请求数据均遵从原始请求包的格式，不进行额外的编码解码。

    :param ip_to_add: 需要添加到封禁列表的IP地址 (字符串格式, e.g., "*******")。
    :return: 操作成功或IP已存在则返回 True，否则返回 False。
    """
    print(f"--- 开始执行封禁IP '{ip_to_add}' 的任务 ---")
    base_url = f"https://{CONFIG['target_ip']}/cgi/maincgi.cgi"
    
    # 1. 创建会话并登录
    session = requests.Session()
    session.headers.update({
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    })
    
    print("第一步：正在尝试登录...")
    login_payload = {
        "username": CONFIG['username'],
        "passwd": CONFIG['password'],
        "loginSubmitIpt": ""
    }
    
    try:
        login_resp = session.post(
            f"{base_url}?Url=Index",
            data=login_payload,
            verify=False,
            timeout=10
        )
        login_resp.raise_for_status()
        
        if "username" in login_resp.text and "passwd" in login_resp.text:
            print("❌ 登录失败！请检查用户名和密码。")
            return False
        
        print("✅ 登录成功！")

    except requests.exceptions.RequestException as e:
        print(f"❌ 登录请求失败: {e}")
        return False

    # 2. 查询已封禁的IP地址
    print("\n第二步：正在查询已封禁的IP列表...")
    edit_page_url = f"{base_url}?Url=HostObj&Act=Edit&Name={CONFIG['host_name_encoded']}"

    try:
        get_ip_resp = session.get(edit_page_url, verify=False, timeout=10)
        get_ip_resp.raise_for_status()
        
        match = re.search(r"var ip_str = '(.*?)';", get_ip_resp.text, re.S)
        
        if not match:
            print("❌ 无法在页面上找到已封禁的IP列表 (未匹配到 'var ip_str')。")
            return False
        
        ip_string = match.group(1).strip()
        old_ips = ip_string.split() if ip_string else []
        
        print(f"✅ 查询成功！当前已封禁的IP: {old_ips}")

    except requests.exceptions.RequestException as e:
        print(f"❌ 查询IP列表失败: {e}")
        return False

    # 3. 新增IP并提交
    if ip_to_add in old_ips:
        print(f"\nℹ️  IP地址 '{ip_to_add}' 已存在于封禁列表中，无需重复添加。")
        return True # 目标已达成，视为成功
        
    print(f"\n第三步：正在添加新IP '{ip_to_add}' 并提交...")

    # 手动构建原始的POST请求体字符串
    payload_parts = [
        f"def_host_name={CONFIG['host_name_encoded']}",
        "def_host_mac=00%3A00%3A00%3A00%3A00%3A00"
    ]
    
    all_ips = old_ips + [ip_to_add]
    for ip in all_ips:
        payload_parts.append(f"def_host_ipad={ip}")
        
    payload_parts.extend([
        f"host_ipad_input={ip_to_add}",
        f"name_hidden={CONFIG['host_name_encoded']}",
        "def_host_frompage=",
        "def_host_from=",
        "def_host_edt_but=+%C8%B7%B6%A8+"
    ])
    
    final_payload_string = "&".join(payload_parts)

    try:
        update_headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Origin": f"https://{CONFIG['target_ip']}",
            "Referer": edit_page_url
        }
        
        update_resp = session.post(
            edit_page_url, 
            data=final_payload_string.encode('utf-8'),
            headers=update_headers,
            verify=False, 
            timeout=10
        )
        update_resp.raise_for_status()

        if update_resp.status_code == 200:
            print(f"✅ 提交成功！IP '{ip_to_add}' 已被添加。")
            return True
        else:
            print(f"❌ 提交失败，服务器返回状态码: {update_resp.status_code}")
            return False

    except requests.exceptions.RequestException as e:
        print(f"❌ 提交更新请求失败: {e}")
        return False


if __name__ == "__main__":
    # --- 函数调用示例 ---

    # 示例1：封禁一个新的IP
    ip_to_block_1 = "*******"
    success1 = add_ip_to_blocklist(ip_to_block_1)
    if success1:
        print(f"\n--- 任务处理完成，结果：成功处理IP {ip_to_block_1} ---\n")
    else:
        print(f"\n--- 任务处理失败，结果：未能处理IP {ip_to_block_1} ---\n")
